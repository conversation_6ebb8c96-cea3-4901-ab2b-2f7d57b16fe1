<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.telecom.apigateway.mapper.SensitiveLogMapper">

    <select id="statTopRuleOfContent"
            resultType="com.telecom.apigateway.model.vo.response.SensitiveTopResponse$TopData">
        select rule_id as label, count(1) as count
        from (select rule_id, content
              from sensitive_log
              where log_time between #{startTime} and  #{endTime}
              and rule_id in (select id from sensitive_rule where is_deleted = false)
              group by rule_id, content) t
        group by rule_id
        order by count desc
        limit #{queryCount}
    </select>

    <select id="sumContentOfRule" resultType="int">
        select sum(count)
        from (select count(1) as count
              from (select rule_id, content
                    from sensitive_log
                    where log_time between #{startTime} and  #{endTime}
                    and rule_id in (select id from sensitive_rule where is_deleted = false)
                    group by rule_id, content) t
              group by rule_id) t1
    </select>

    <select id="queryWithMergeApi" resultType="com.telecom.apigateway.model.entity.SensitiveLog">
        select coalesce(a.merge_id, sensitive_log.api_id) as api_id
        from sensitive_log
                 left join api a on a.id = sensitive_log.api_id
        group by api_id
    </select>
</mapper>
