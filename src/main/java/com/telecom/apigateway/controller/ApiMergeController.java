package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.model.vo.request.AddApiMergeRequest;
import com.telecom.apigateway.model.vo.request.MatchApiMergeRequest;
import com.telecom.apigateway.model.vo.request.QueryApiMergeRequest;
import com.telecom.apigateway.model.vo.request.UpdateApiMergeRequest;
import com.telecom.apigateway.model.vo.response.ApiMergeAnalyzeResponse;
import com.telecom.apigateway.model.vo.response.MatchMergeApiResponse;
import com.telecom.apigateway.model.vo.response.QueryApiMergeResponse;
import com.telecom.apigateway.service.ApiMergeBizService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/api/merge")
@Tag(name = "api 合并策略")
@CheckApiPermission("API_LIST")
public class ApiMergeController {

    @Resource
    private ApiMergeBizService apiMergeBizService;

    @Operation(summary = "分页查询")
    @GetMapping("/page")
    public Result<Page<QueryApiMergeResponse>> page(@ModelAttribute QueryApiMergeRequest query) {
        Page<QueryApiMergeResponse> page = apiMergeBizService.queryPage(query);
        return Result.success(page);
    }

    @Operation(summary = "详情")
    @GetMapping("/{id}")
    public Result<QueryApiMergeResponse> detail(@PathVariable String id) {
        QueryApiMergeResponse detail = apiMergeBizService.getByMergeId(id);
        return Result.success(detail);
    }

    @Operation(summary = "匹配资产")
    @PostMapping("/match")
    public Result<List<MatchMergeApiResponse>> match(@RequestBody MatchApiMergeRequest request) {
        List<MatchMergeApiResponse> list = apiMergeBizService.match(request);
        return Result.success(list);
    }

    @Operation(summary = "save")
    @PostMapping("")
    public Result<String> save(@RequestBody AddApiMergeRequest request) {
        ApiMerge detail = apiMergeBizService.saveOne(request);
        return Result.success(detail.getId());
    }

    @Operation(summary = "update")
    @PutMapping("")
    public Result<String> update(@RequestBody UpdateApiMergeRequest request) {
        ApiMerge detail = apiMergeBizService.updateOne(request);
        return Result.success(detail.getId());
    }

    @Operation(summary = "enable")
    @PutMapping("/{id}/enable")
    public Result<String> enable(@PathVariable String id) {
        ApiMerge detail = apiMergeBizService.enable(id);
        return Result.success(detail.getId());
    }

    @Operation(summary = "disable")
    @PutMapping("/{id}/disable")
    public Result<String> disable(@PathVariable String id) {
        ApiMerge detail = apiMergeBizService.disable(id);
        return Result.success(detail.getId());
    }


    @Operation(summary = "delete")
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable String id) {
        ApiMerge detail = apiMergeBizService.deleteOne(id);
        return Result.success(detail.getId());
    }

    @Operation(summary = "enable")
    @PutMapping("/enable/batch")
    public Result<String> enableBatch(@RequestBody Map<String, List<String>> req) {
        apiMergeBizService.enableBatch(req.get("ids"));
        return Result.success("");
    }

    @Operation(summary = "disable")
    @PutMapping("/disable/batch")
    public Result<String> disableBatch(@RequestBody Map<String, List<String>> req) {
        apiMergeBizService.disableBatch(req.get("ids"));
        return Result.success("");
    }


    @Operation(summary = "delete")
    @DeleteMapping("/batch")
    public Result<String> deleteBatch(@RequestBody Map<String, List<String>> req) {
        apiMergeBizService.deleteBatch(req.get("ids"));
        return Result.success("");
    }

    @Operation(summary = "analyze")
    @PostMapping("/analyze")
    public Result<ApiMergeAnalyzeResponse> analyze(@RequestBody Map<String, List<String>> req) {
        ApiMergeAnalyzeResponse response = apiMergeBizService.tryMerge(req.get("apiIds"));
        return Result.success(response);
    }
}
