package com.telecom.apigateway.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.CheckApiPermission;
import com.telecom.apigateway.common.Result;
import com.telecom.apigateway.model.vo.request.QuerySensitivePortraitLeakRequest;
import com.telecom.apigateway.model.vo.request.QuerySensitivePortraitLogRequest;
import com.telecom.apigateway.model.vo.response.SensitiveLatestDataResponse;
import com.telecom.apigateway.model.vo.response.SensitivePortraitLogResponse;
import com.telecom.apigateway.model.vo.response.SensitiveTopResponse;
import com.telecom.apigateway.service.SensitivePortraitDbService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-22
 */
@RestController
@RequestMapping("/api/sensitive/portrait")
@Tag(name = "涉敏画像接口")
@CheckApiPermission(value = "SENSITIVE_PORTRAIT")
public class SensitivePortraitController {

    @Resource
    private SensitivePortraitDbService sensitivePortraitDbService;

    @Operation(summary = "最新泄露数据")
    @GetMapping("/latest")
    public Result<List<SensitiveLatestDataResponse>> latestData() {
        List<SensitiveLatestDataResponse> sensitiveLatestDataResponse = sensitivePortraitDbService.latestData();
        return Result.success(sensitiveLatestDataResponse);
    }

    @Operation(summary = "泄露top")
    @GetMapping("/top")
    public Result<SensitiveTopResponse> top() {
        SensitiveTopResponse sensitiveLatestDataResponse = sensitivePortraitDbService.top();
        return Result.success(sensitiveLatestDataResponse);
    }

    @Operation(summary = "敏感数据查询")
    @GetMapping("/search")
    public Result<Page<?>> search(@RequestParam String type,
                                  @RequestParam List<String> keywords,
                                  @RequestParam Integer pageNum,
                                  @RequestParam Integer pageSize) {
        return Result.success(sensitivePortraitDbService.search(type, keywords, pageNum, pageSize));
    }

    @Operation(summary = "敏感泄露详情")
    @GetMapping("/detail")
    public Result<Object> detail(@RequestParam String type, @RequestParam String keyword,
                                 @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
                                 @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        return Result.success(sensitivePortraitDbService.getDetail(type, keyword, startTime, endTime));
    }

    @Operation(summary = "敏感泄露图表")
    @GetMapping("/chart")
    public Result<Object> chart(@RequestParam String type, @RequestParam String keyword,
                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        return Result.success(sensitivePortraitDbService.getCharts(type, keyword, startTime, endTime));
    }

    @Operation(summary = "敏感日志")
    @GetMapping("/log")
    public Result<Page<SensitivePortraitLogResponse>> queryLog(@ModelAttribute QuerySensitivePortraitLogRequest query) {
        return Result.success(sensitivePortraitDbService.queryLog(query));
    }

    @Operation(summary = "泄露记录")
    @GetMapping("/leak/stat")
    public Result<Object> statAll(@ModelAttribute QuerySensitivePortraitLeakRequest query) {
        return Result.success(sensitivePortraitDbService.statLeak(query));
    }
}
