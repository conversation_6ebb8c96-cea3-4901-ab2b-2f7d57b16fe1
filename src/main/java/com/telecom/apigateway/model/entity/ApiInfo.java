package com.telecom.apigateway.model.entity;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.config.mybatisplus.StringListTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * api info
 *
 * <AUTHOR> Denty
 * @date : 2024/8/13
 */
@Data
@TableName(value = "api", autoResultMap = true)
@Schema(description = "api info")
public class ApiInfo implements Serializable {
    private static final long serialVersionUID = 164514535681554941L;

    @Schema(description = "api id")
    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 合并策略的id
     */
    protected String mergeId;

    @Schema(description = "api 名称, 默认等于 uri")
    protected String name;

    @Schema(description = "所用应用 id")
    protected String appId;

    @Schema(description = "多个, http method,GET POST PUT DELETE")
    @TableField(typeHandler = StringListTypeHandler.class)
    protected List<String> httpMethods;

    @TableField(exist = false)
    protected String hostName;

    @Schema(description = "uri, 可能是路径参数格式")
    protected String uri;

    @Schema(description = "来源, 0自动 1手动")
    protected Integer source;
    /**
     * 具有不可信性, 原子性可能无保障, 建议关联查询进行匹配
     */
    @Schema(description = "涉敏等级, 0安全/1低/2中/3高")
    @Deprecated
    protected Integer sensitiveLevel;

    @Schema(description = "是否在线")
    protected Boolean isOnline;

    @Schema(description = "是否加密")
    @TableField(value = "is_encrypt")
    protected Boolean encrypted;

    @Schema(description = "是否激活")
    protected Boolean isActive;

    @Schema(description = "是否删除")
    @TableField(value = "is_deleted")
    @JsonIgnore
    protected Boolean deleted;

    @Schema(description = "上线时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    protected LocalDateTime onlineTime;

    @Schema(description = "发现时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    protected LocalDateTime discoverTime;

    @Schema(description = "创建人")
    protected String createUser;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    protected LocalDateTime createTime;

    @Schema(description = "更新人")
    protected String updateUser;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    protected LocalDateTime updateTime;

    @Schema(description = "备注")
    protected String remark;

    @Schema(description = "主应用id")
    protected String mainApplicationId;

    @TableField(exist = false)
    protected List<String> httpHosts;


    public static ApiInfo ofMerge(String id,
                                  String apiName,
                                  String appId,
                                  List<String> httpMethod,
                                  String uri) {
        ApiInfo apiInfo = new ApiInfo();

        apiInfo.id = id;
        apiInfo.mergeId = null;
        apiInfo.name = apiName;
        apiInfo.appId = appId;
        apiInfo.mainApplicationId = appId;
        apiInfo.httpMethods = httpMethod;
        apiInfo.uri = uri;

        apiInfo.isOnline = false;
        apiInfo.isActive = true;
        apiInfo.deleted = false;
        apiInfo.onlineTime = null;
        LocalDateTime now = LocalDateTime.now();
        apiInfo.discoverTime = now;
        apiInfo.createTime = now;
        apiInfo.updateTime = now;
        apiInfo.createUser = StpUtil.getLoginIdAsString();
        apiInfo.updateUser = StpUtil.getLoginIdAsString();
        apiInfo.setSource(Constant.Api.SOURCE_APP_MERGE);

        return apiInfo;
    }

    public List<String> toRecognizedStrings() {
        List<String> list = new ArrayList<>();
        for (String httpMethod : httpMethods) {
            for (String httpHost : getHttpHosts()) {
                list.add(httpMethod + ":" + httpHost + uri);
            }
        }
        return list;
    }

}
