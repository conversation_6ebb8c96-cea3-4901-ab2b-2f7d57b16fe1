package com.telecom.apigateway.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.telecom.apigateway.config.mybatisplus.JsonbListTypeHandler;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.UrlEndpoint;
import com.telecom.apigateway.utils.ApiUrlUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * api info
 *
 * <AUTHOR> Denty
 * @date : 2024/8/13
 */
@Data
@TableName(autoResultMap = true)
@Schema(description = "api info")
public class ApiFullInfo extends ApiInfo implements Serializable {

    private static final long serialVersionUID = -3184640267608335079L;
    /**
     * 来自应用的 uri 前面的部分
     */
    @TableField(typeHandler = JsonbListTypeHandler.class)
    private List<UrlEndpoint> urlEndpoints;

    private List<String> httpHosts;
    /**
     * api 的完整 url
     */
    private List<String> urls;

    public boolean equals(EsNginxDTO ngLog) {
        return equals(ngLog.getHttpMethod(), ngLog.getHttpHost(), ngLog.getUri());
    }

    public boolean equals(String httpMethod, String httpHost, String uri) {
        if (this.getHttpMethods().stream().noneMatch(method -> method.equalsIgnoreCase(httpMethod))) {
            return false;
        }
        if (this.urlEndpoints.stream().noneMatch(url -> (url.getHost() + url.getPort()).equals(httpHost))) {
            return false;
        }
        if (this.uri.equals(uri)) {
            return true;
        }
        return uri.contains("{") && uri.contains("}") && ApiUrlUtils.isMatch(this.uri, uri);
    }

    public boolean equals(String httpMethod, String host, Integer port, String uri) {
        if (this.httpMethods.stream().noneMatch(method -> method.equalsIgnoreCase(httpMethod))) {
            return false;
        }
        if (this.urlEndpoints.stream().noneMatch(url -> url.getHost().equals(host))) {
            return false;
        }
        if (this.urlEndpoints.stream().noneMatch(url -> url.getPort().equals(String.valueOf(port)))) {
            return false;
        }
        if (this.uri.equals(uri)) {
            return true;
        }
        return this.uri.contains("{") && this.uri.contains("}") && ApiUrlUtils.isMatch(this.uri, uri);
    }
}
