package com.telecom.apigateway.model.vo.response;

import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.enums.ApplicationSourceEnum;
import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import com.telecom.apigateway.model.enums.ProtocolEnum;
import com.telecom.apigateway.model.enums.RiskLevelEnum;
import lombok.Data;

import java.util.Arrays;
import java.util.List;

@Data
public class ApplicationDetailResponse {
    private String applicationId;
    private String name;
    private ApiCountResponse apiCount;
    private String host;
    private String remark;
    private ProtocolEnum protocol;
    private String owner;
    private String phone;
    private String email;
    private String port;
    private String parentId;
    private String parentName;
    private ApplicationTypeEnum type;
    private String uri;
    private String url;
    private List<String> area;

    // TODO 近30天威胁数量 近30天涉敏数量
    private List<BaseLabelResponse> threatCount;
    private List<BaseLabelResponse> sensitiveCount;
    private ApplicationSourceEnum source;
    private String correctPolicyId;

//    TODO
    public static ApplicationDetailResponse convertApplicationResponse(Application application) {
        ApplicationDetailResponse applicationResponse = new ApplicationDetailResponse();
        applicationResponse.setApplicationId(application.getApplicationId());
        applicationResponse.setName(application.getName());
        applicationResponse.setApiCount(new ApiCountResponse());
//        applicationResponse.setHost(application.getHost());
        applicationResponse.setRemark(application.getRemark());
//        applicationResponse.setPort(application.getPort());
//        applicationResponse.setProtocol(application.getProtocol());
        applicationResponse.setParentId(application.getParentId());
        applicationResponse.setType(application.getType());
//        applicationResponse.setUri(application.getUri());
//        applicationResponse.setUrl(String.format("%s:%s%s", application.getHost(), application.getPort(), application.getUri()));
        applicationResponse.setOwner(application.getOwner());
        applicationResponse.setArea(Arrays.asList(application.getArea()));
        applicationResponse.setPhone(application.getPhone());
        applicationResponse.setEmail(application.getEmail());
        applicationResponse.setSource(application.getSource());
        applicationResponse.setCorrectPolicyId(application.getCorrectPolicyId());
        return applicationResponse;
    }

    public ApplicationDetailResponse setThreatCountAndSensitiveCount(List<BaseLabelResponse> threatCount, List<BaseLabelResponse> sensitiveCount) {
        this.threatCount = threatCount;
        this.sensitiveCount = sensitiveCount;
        return this;
    }
}
