package com.telecom.apigateway.model.vo.request;

import com.telecom.apigateway.model.entity.ApiMergeCondition;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;

public class MatchApiMergeRequest implements Serializable {

    private static final long serialVersionUID = 5624731695128615193L;

    @Schema(description = "应用ID")
    private String appId;

    @Schema(description = "匹配条件")
    private List<ApiMergeCondition> condition;

    @Schema(description = "匹配URL路径")
    private String urlReg;

    @Schema(description = "匹配HTTP请求方式")
    private List<String> httpMethods;

    public List<ApiMergeCondition> getCondition() {
        return condition;
    }

    public void setCondition(List<ApiMergeCondition> condition) {
        this.condition = condition;
    }

    public String getUrlReg() {
        return urlReg;
    }

    public void setUrlReg(String urlReg) {
        this.urlReg = urlReg;
    }

    public List<String> getHttpMethods() {
        return httpMethods;
    }

    public void setHttpMethods(List<String> httpMethods) {
        this.httpMethods = httpMethods;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }
}
