package com.telecom.apigateway.model.vo.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.config.mybatisplus.JsonbListTypeHandler;
import com.telecom.apigateway.model.dto.PolicyConditionDTO;
import com.telecom.apigateway.model.entity.Application;
import com.telecom.apigateway.model.entity.ApplicationCorrectPolicy;
import com.telecom.apigateway.model.enums.CorrectPolicyActionEnum;
import com.telecom.apigateway.model.enums.CorrectPolicyStatusEnum;
import cn.hutool.json.JSONUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应用修正策略响应
 */
@Data
@Schema(description = "应用修正策略响应")
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationCorrectPolicyResponse {
    
    @Schema(description = "策略ID")
    private String policyId;
    
    @Schema(description = "策略名称")
    private String policyName;
    
    @Schema(description = "策略状态")
    private CorrectPolicyStatusEnum status;
    
    @Schema(description = "策略动作")
    private CorrectPolicyActionEnum action;
    
    @Schema(description = "策略条件列表")
    private List<PolicyConditionDTO> conditions;

    @Schema(description = "策略路径")
    private String path;

    @Schema(description = "策略条件文本")
    private String conditionText;

    @Schema(description = "合并后的应用名称")
    private String mergedName;

    @Schema(description = "合并后主id")
    private String relateAppId;
    
    @Schema(description = "是否曾经启用过")
    private Boolean everEnabled;
    
    @Schema(description = "创建人")
    private String createUser;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime createTime;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime updateTime;

    @Schema(description = "分组id")
    private String groupId;
    
    public ApplicationCorrectPolicyResponse(ApplicationCorrectPolicy policy, Application application) {
        this.policyId = policy.getPolicyId();
        this.policyName = policy.getPolicyName();
        this.status = policy.getStatus();
        this.action = policy.getAction();
        this.conditions = policy.getConditions();
        this.mergedName = application.getName();
        this.relateAppId = application.getApplicationId();
        this.everEnabled = policy.getEverEnabled();
        this.createUser = policy.getCreateUser();
        this.createTime = policy.getCreateTime();
        this.updateTime = policy.getUpdateTime();
        this.path = policy.getConditions().get(0).getUri();
        this.groupId = policy.getGroupId();
        this.conditionText = policy.getConditions().stream().map(PolicyConditionDTO::getFullUrl).collect(Collectors.joining(", "));
    }

    public ApplicationCorrectPolicyResponse(ApplicationCorrectPolicy policy) {
        this.policyId = policy.getPolicyId();
        this.policyName = policy.getPolicyName();
        this.status = policy.getStatus();
        this.action = policy.getAction();
        this.conditions = JSONUtil.toList(JSONUtil.parseArray(policy.getConditions()), PolicyConditionDTO.class);
        this.everEnabled = policy.getEverEnabled();
        this.createUser = policy.getCreateUser();
        this.createTime = policy.getCreateTime();
        this.updateTime = policy.getUpdateTime();
        this.mergedName = policy.getMergedName();
        this.path = policy.getConditions().get(0).getUri();
        this.groupId = policy.getGroupId();
        this.conditionText = policy.getConditions().stream().map(PolicyConditionDTO::getFullUrl).collect(Collectors.joining(", "));
    }
} 