package com.telecom.apigateway.model.vo.response;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.config.StringListTypeHandler;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-10-29
 */
@Data
public class SensitiveApiQueryResponse {
    @Schema(description = "主键")
    @TableId(type = IdType.ASSIGN_ID)
    private String id;
    @Schema(description = "api的id")
    private String apiId;
    @Schema(description = "app 的id")
    private String appId;
    @Schema(description = "api的uri")
    private String uri;
    @Schema(description = "应用名称")
    private String appName;
    @Schema(description = "api的url")
    private String url;

    @Schema(description = "敏感标签")
    private String sensitiveTag;
    @Schema(description = "敏感级别")
    private Integer sensitiveLevel;
    @Schema(description = "敏感次数")
    private Integer sensitiveCount;

    @Schema(description = "上次敏感时间")
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime lastSensitiveTime;

    @Schema(description = "日志时间")
    @JsonFormat(pattern = Constant.DATE_TIME_PATTERN)
    @DateTimeFormat(pattern = Constant.DATE_TIME_PATTERN)
    private LocalDateTime discoverTime;
    @Schema(description = "是否处理")
    private Boolean dealt;

    @Schema(description = "主机")
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> hosts;
    @Schema(description = "端口")
    private Integer port;

    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> httpHosts;

}
