package com.telecom.apigateway.service;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.telecom.apigateway.client.NginxLogEsClient;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.mapper.SensitiveApiMapper;
import com.telecom.apigateway.model.dto.EsNginxDTO;
import com.telecom.apigateway.model.dto.EsQueryDTO;
import com.telecom.apigateway.model.dto.EsSensitiveRuleDTO;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.SensitiveApi;
import com.telecom.apigateway.model.entity.SensitiveRule;
import com.telecom.apigateway.model.vo.request.QuerySensitiveApiRequest;
import com.telecom.apigateway.model.vo.response.ApplicationResponse;
import com.telecom.apigateway.model.vo.response.SensitiveApiQueryResponse;
import com.telecom.apigateway.model.vo.response.SensitiveKeywordResponse;
import com.telecom.apigateway.utils.DateTimeUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-10-25
 */
@Service
public class SensitiveApiService extends ServiceImpl<SensitiveApiMapper, SensitiveApi> {

    @Resource
    private SensitiveRuleService sensitiveRuleService;
    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private NginxLogEsClient nginxLogEsClient;
    @Resource
    private ApplicationService applicationService;
    @Resource
    private ApplicationBusinessService applicationBusinessService;

    @Override
    public List<SensitiveApi> list() {
        return lambdaQuery().eq(SensitiveApi::getDeleted, false).list();
    }

    public IPage<SensitiveApiQueryResponse> queryPage(QuerySensitiveApiRequest request) {
        Pair<LocalDateTime, LocalDateTime> timePair = DateTimeUtils.rangeTime(request.getRange(),
                request.getStartTime(), request.getEndTime());
        request.setStartTime(timePair.getLeft());
        request.setEndTime(timePair.getRight());

        List<String> appIds = getAppIds(request.getAppIds());
        request.setAppIds(appIds);
        IPage<SensitiveApiQueryResponse> page = baseMapper.queryPage(new Page<>(request.getPageNum(),
                request.getPageSize()), request);

        List<ApplicationResponse> applicationResponses = applicationBusinessService.listWithBizName();
        // map,  applicationId 和 AppName
        Map<String, String> applicationResponseMap =
                applicationResponses.stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId,
                        ApplicationResponse::getName, (a, b) -> a));

        page.getRecords().forEach(item ->
                item.setAppName(applicationResponseMap.getOrDefault(item.getAppId(), "未知")));
        return page;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deal(String id) {
        // 更新处置状态
        SensitiveApi sensitiveApi = this.getById(id);
        if (sensitiveApi == null) {
            throw new BusinessException(ResultCodeEnum.SENSITIVE_API_NOT_EXISTED);
        }
        this.lambdaUpdate()
                .set(SensitiveApi::getDealt, true)
                .set(SensitiveApi::getDealSensitiveTime, LocalDateTime.now())
                .set(SensitiveApi::getUpdateTime, LocalDateTime.now())
                .set(SensitiveApi::getUpdateUser, StpUtil.getLoginIdAsString())
                .set(SensitiveApi::getDealt, true)
                .eq(SensitiveApi::getId, id)
                .update();
        // 查询出 SensitiveApi 表中最大的涉敏等级,如果没有等级, 则置为0
        Integer maxApiSensitiveLevel = baseMapper.getMaxApiSensitiveLevelByApiId(sensitiveApi.getApiId());
        ApiInfo apiInfo = new ApiInfo();
        apiInfo.setId(id);
        apiInfo.setSensitiveLevel(maxApiSensitiveLevel == null ? 0 : maxApiSensitiveLevel);
        apiInfoService.updateById(apiInfo);
    }

    public List<SensitiveKeywordResponse> getKeywordByIp(String ip) {
        List<SensitiveRule> ruleList = sensitiveRuleService.lambdaQuery()
                .eq(SensitiveRule::getIsDeleted, false).list();
        Set<String> ruleIds = ruleList.stream().map(SensitiveRule::getId).collect(Collectors.toSet());
        // map code 和 名称
        Map<String, String> ruleMap = ruleList.stream().collect(Collectors.toMap(SensitiveRule::getId,
                SensitiveRule::getName));

        EsQueryDTO queryDTO = EsQueryDTO.builder()
                .queryCount(10000)
                .build()
                .addQuery("clientIp", ip)
                .addExistQuery("sensitiveRules")
                .addMultipleQuery("sensitiveRules.ruleId", ruleIds)
                .orderBy("logTime", SortOrder.DESC);
        List<EsNginxDTO> queryResult = nginxLogEsClient.query(queryDTO);
        Map<String, SensitiveKeywordResponse> map = new HashMap<>();

        // map,  applicationId 和 AppName
        List<ApplicationResponse> applicationResponses = applicationBusinessService.listWithBizName();
        Map<String, String> applicationResponseMap =
                applicationResponses.stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId,
                        ApplicationResponse::getName, (a, b) -> a));

        for (int i = queryResult.size() - 1; i >= 0; i--) {
            EsNginxDTO esNginxDTO = queryResult.get(i);
            for (EsSensitiveRuleDTO sensitiveRule : esNginxDTO.getSensitiveRules()) {
                if (!applicationResponseMap.containsKey(esNginxDTO.getAppId())) {
                    continue;
                }
                String key = esNginxDTO.getAppId() + sensitiveRule.getRuleId() + sensitiveRule.getContent();
                if (map.containsKey(key)) {
                    SensitiveKeywordResponse d = map.get(key);
                    d.setCount(d.getCount() + 1);
                    d.setUpdateTime(esNginxDTO.getLogTime());
                    map.put(key, d);
                    continue;
                }
                SensitiveKeywordResponse d = new SensitiveKeywordResponse();
                d.setCreateTime(esNginxDTO.getLogTime());
                d.setAppName(applicationResponseMap.getOrDefault(esNginxDTO.getAppId(), "未知"));
                d.setKeyword(sensitiveRule.getContent());
                d.setSensitiveTag(ruleMap.get(sensitiveRule.getRuleId()));
                d.setCount(1);
                d.setUpdateTime(esNginxDTO.getLogTime());
                map.put(key, d);
            }
        }
        List<SensitiveKeywordResponse> keys = new ArrayList<>();
        map.forEach((k, v) -> keys.add(v));
        // 根据 创建时间排序
        keys.sort((o1, o2) -> o2.getUpdateTime().compareTo(o1.getUpdateTime()));
        return keys;
    }

    private List<String> getAppIds(List<String> appIds) {
        List<String> results = new ArrayList<>();
        if (CollUtil.isEmpty(appIds)) {
            results = StpUtil.getPermissionList();
        } else {
            results = applicationService.getIdsWithChildren(appIds);
        }
        if (CollUtil.isEmpty(results)) {
            results.add("-1");
        }
        return results;
    }

    public void resetLevel(String apiId) {
        baseMapper.resetLevel(apiId);
    }

    public void resetCount(List<String> apiId) {
        if (CollUtil.isEmpty(apiId)) return;
        lambdaUpdate()
                .set(SensitiveApi::getSensitiveCount, 0)
                .set(SensitiveApi::getNonsensitiveCount, 0)
                .in(SensitiveApi::getApiId, apiId)
                .update();
    }

    public void deleteByRuleId(String ruleId) {
        lambdaUpdate()
                .set(SensitiveApi::getDeleted, true)
                .set(SensitiveApi::getUpdateTime, LocalDateTime.now())
                .set(SensitiveApi::getUpdateUser, StpUtil.getLoginIdAsString())
                .eq(SensitiveApi::getSensitiveRuleId, ruleId)
                .update();
    }

    public void deleteByRuleIds(Collection<String> ruleIds) {
        if (CollUtil.isEmpty(ruleIds)) return;
        lambdaUpdate()
                .set(SensitiveApi::getDeleted, true)
                .set(SensitiveApi::getUpdateTime, LocalDateTime.now())
                .set(SensitiveApi::getUpdateUser, StpUtil.getLoginIdAsString())
                .in(SensitiveApi::getSensitiveRuleId, ruleIds)
                .update();
    }

    public void deleteByApiId(String apiId) {
        lambdaUpdate()
                .set(SensitiveApi::getDeleted, true)
                .eq(SensitiveApi::getApiId, apiId)
                .update();
    }

    public void updateApiId(List<String> fromApiIds, String toApiId) {
        if (CollUtil.isEmpty(fromApiIds) || StrUtil.isBlank(toApiId)) {
            return;
        }
        lambdaUpdate()
                .set(SensitiveApi::getApiId, toApiId)
                .set(SensitiveApi::getSensitiveCount, 0) // 重置次数
                .set(SensitiveApi::getNonsensitiveCount, 0) // 重置次数
                .set(SensitiveApi::getUpdateTime, LocalDateTime.now())
                .set(SensitiveApi::getUpdateUser, StpUtil.getLoginIdAsString())
                .in(SensitiveApi::getApiId, fromApiIds)
                .update();
        // todo 是否要去重?
    }
}

