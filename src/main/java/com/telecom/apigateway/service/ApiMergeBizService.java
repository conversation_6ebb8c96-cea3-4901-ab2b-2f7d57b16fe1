package com.telecom.apigateway.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.AntPathMatcher;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.ResultCodeEnum;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.model.entity.ApiFullInfo;
import com.telecom.apigateway.model.entity.ApiInfo;
import com.telecom.apigateway.model.entity.ApiMerge;
import com.telecom.apigateway.model.entity.ApiMergeCondition;
import com.telecom.apigateway.model.entity.UserInfo;
import com.telecom.apigateway.model.enums.ApiMergeEnum;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.vo.request.AddApiMergeRequest;
import com.telecom.apigateway.model.vo.request.MatchApiMergeRequest;
import com.telecom.apigateway.model.vo.request.QueryApiMergeRequest;
import com.telecom.apigateway.model.vo.request.UpdateApiMergeRequest;
import com.telecom.apigateway.model.vo.response.ApiMergeAnalyzeResponse;
import com.telecom.apigateway.model.vo.response.ApplicationResponse;
import com.telecom.apigateway.model.vo.response.MatchMergeApiResponse;
import com.telecom.apigateway.model.vo.response.QueryApiMergeResponse;
import com.telecom.apigateway.service.impl.NginxAccessLogService;
import com.telecom.apigateway.utils.ApiUrlUtils;
import com.telecom.apigateway.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class ApiMergeBizService {

    @Resource
    private ApiMergeService apiMergeService;
    @Resource
    private ApiInfoService apiInfoService;
    @Resource
    private ApplicationBusinessService applicationBusinessService;
    @Resource
    private SensitiveLogService sensitiveLogService;
    @Resource
    private SensitiveApiService sensitiveApiService;
    @Resource
    private NginxAccessLogService nginxAccessLogService;
    @Resource
    private AbnormalBehaviorRuleService abrService;
    @Resource
    private AbnormalBehaviorRuleTriggerService abrtService;
    @Resource
    private RiskLogNewService riskLogNewService;
    @Resource
    private ApiDecryptService apiDecryptService;
    @Resource
    private BlocklistService blocklistService;
    @Resource
    private SensitiveWhiteListService sensitiveWhiteListService;
    @Resource
    private UserInfoService userInfoService;

    private static final AntPathMatcher pathMatcher = new AntPathMatcher();

    public QueryApiMergeResponse getByMergeId(String id) {
        ApiMerge byId = apiMergeService.getById(id);
        QueryApiMergeResponse response = entityToResponse(byId);
        return response;
    }

    public Page<QueryApiMergeResponse> queryPage(QueryApiMergeRequest pageRequest) {
        Page<ApiMerge> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        Page<ApiMerge> pageData = apiMergeService.lambdaQuery()
                .like(StrUtil.isNotBlank(pageRequest.getName()), ApiMerge::getName, pageRequest.getName())
                .eq(pageRequest.getPolicy() != null, ApiMerge::getPolicy, pageRequest.getPolicy())
                .eq(pageRequest.getEnable() != null, ApiMerge::getEnable, pageRequest.getEnable())
                .orderByDesc(ApiMerge::getCreateTime)
                .page(page);

        List<UserInfo> users = userInfoService.list();
        Map<String, String> userMap = users.stream().collect(Collectors.toMap(UserInfo::getUsername,
                UserInfo::getRealName));

        List<QueryApiMergeResponse> list =
                pageData.getRecords().stream().map(this::entityToResponse)
                        .peek(apiMerge -> apiMerge.setUpdateUser(userMap.get(apiMerge.getUpdateUser())))
                        .collect(Collectors.toList());

        return PageUtils.convertPage(page, list);
    }

    private QueryApiMergeResponse entityToResponse(ApiMerge apiMerge) {
        QueryApiMergeResponse apiMergeResponse = new QueryApiMergeResponse();
        apiMergeResponse.setId(apiMerge.getId());
        apiMergeResponse.setName(apiMerge.getName());
        apiMergeResponse.setAppId(apiMerge.getAppId());
        apiMergeResponse.setApiName(apiMerge.getApiName());
        apiMergeResponse.setPolicy(apiMerge.getPolicy());
        apiMergeResponse.setCondition(apiMerge.getCondition());
        apiMergeResponse.setUriReg(apiMerge.getUriReg());
        apiMergeResponse.setHttpMethods(apiMerge.getHttpMethods());
        apiMergeResponse.setEnable(apiMerge.getEnable());
        apiMergeResponse.setEditable(apiMerge.getEditable());
        apiMergeResponse.setCreateUser(apiMerge.getCreateUser());
        apiMergeResponse.setCreateTime(apiMerge.getCreateTime());
        apiMergeResponse.setUpdateUser(apiMerge.getUpdateUser());
        apiMergeResponse.setUpdateTime(apiMerge.getUpdateTime());
        apiMergeResponse.setRemark(apiMerge.getRemark());
        apiMergeResponse.setDetail(apiMerge.getDetail());
        return apiMergeResponse;
    }

    public List<MatchMergeApiResponse> match(MatchApiMergeRequest request) {
        List<ApiMergeCondition> conditions = request.getCondition();
        String urlReg = request.getUrlReg();
        List<String> httpMethods = request.getHttpMethods();

        List<ApplicationResponse> apps = applicationBusinessService.listWithBizName();
        Map<String, String> appMap = apps.stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId,
                ApplicationResponse::getName, (a, b) -> a));

        List<ApiFullInfo> apis = apiInfoService.listWithAppOfNotMerge(request.getAppId());

        if (CollUtil.isNotEmpty(conditions)) {
            return apis.stream().filter(api -> {
                boolean flag = true;
                for (ApiMergeCondition condition : conditions) {
                    switch (condition.getTarget()) {
                        case URI:
                            if (!match(api.getUri(), condition.getValue(), condition.getOperation())) {
                                return false;
                            }
                            break;
                        case HTTP_METHOD:
                            if (!match(api.getHttpMethods(), condition.getValue(), condition.getOperation())) {
                                return false;
                            }
                            break;
                    }
                }
                return flag;
            }).map(api -> {
                MatchMergeApiResponse response = new MatchMergeApiResponse();
                response.setApiId(api.getId());
                response.setApiName(api.getName());
                response.setAppId(api.getAppId());
                response.setAppName(appMap.get(api.getAppId()));
                response.setHttpMethod(api.getHttpMethods());
                response.setUri(api.getUri());
                return response;
            }).collect(Collectors.toList());
        } else if (CollUtil.isNotEmpty(httpMethods) && StrUtil.isNotBlank(urlReg)) {
            URL url;
            try {
                url = new URL(urlReg);
            } catch (MalformedURLException e) {
                throw new BusinessException(ResultCodeEnum.URL_FORMAT_ERROR);
            }
            return apis.stream().filter(api -> {
                        boolean flag = true;
                        if (!new HashSet<>(httpMethods).containsAll(api.getHttpMethods())) {
                            return false;
                        }
                        if (api.getUrlEndpoints().stream()
                                .noneMatch(u -> (u.getHost().equals(url.getHost()) &&
                                        u.getPort().equals(String.valueOf(url.getPort()))))) {
                            return false;
                        }
                        if (!pathMatcher.match(url.getPath(), api.getUri())) {
                            return false;
                        }
                        return flag;
                    }).map(api -> {
                        MatchMergeApiResponse response = new MatchMergeApiResponse();
                        response.setApiId(api.getId());
                        response.setApiName(api.getName());
                        response.setAppId(api.getAppId());
                        response.setAppName(appMap.get(api.getAppId()));
                        response.setHttpMethod(api.getHttpMethods());
                        response.setUri(api.getUri());
                        return response;
                    }).

                    collect(Collectors.toList());
        }
        return null;
    }

    private boolean match(String val1, String val2, ApiMergeEnum.Operation operation) {
        Set<String> val2List = Arrays.stream(val2.split("\n")).map(String::trim).collect(Collectors.toSet());
        switch (operation) {
            case EQUALS:
                return val2List.contains(val1);
            case NOT_EQUALS:
                return !val2List.contains(val1);
            case CONTAINS:
                return val1.contains(val2);
            case WILDCARD:
                String regex = val1.replace(".", "\\.").replace("*", ".*");
                return val2.matches(regex);
            case REGEX:
                return val2.matches(val2);
            case BELONGS_TO_RANGE:
                return val2List.stream().allMatch(e -> {
                    String[] range = e.split("-");
                    int min = Integer.parseInt(range[0]);
                    int max = Integer.parseInt(range[1]);
                    int origVal = Integer.parseInt(val1);
                    return origVal >= min && origVal <= max;
                });
        }
        return false;
    }

    private boolean match(List<String> val1, String val2, ApiMergeEnum.Operation operation) {
        Set<String> val2List = Arrays.stream(val2.split("\n")).map(String::trim).collect(Collectors.toSet());
        switch (operation) {
            case EQUALS:
                return val2List.containsAll(val1);
            case NOT_EQUALS:
                return !val2List.containsAll(val1);
        }
        return false;
    }

    private void checkDuplicateName(String name) {
        if (apiMergeService.getByName(name) != null) {
            throw new BusinessException(ResultCodeEnum.DUPLICATE_MERGE_API);
        }
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.INSERT,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#request.name}"}
    )
    public ApiMerge saveOne(AddApiMergeRequest request) {
        checkDuplicateName(request.getName());
        checkDuplicateMatch(request.getUrlReg());
        ApiMerge entity;
        if (request.getPolicy() == ApiMergeEnum.Policy.MERGE) {
            List<ApiFullInfo> apis = apiInfoService.listWithApp();
            URL url;
            try {
                url = new URL(request.getUrlReg());
            } catch (MalformedURLException e) {
                throw new BusinessException(ResultCodeEnum.URL_FORMAT_ERROR);
            }

            Stream<ApiFullInfo> filterApiStream = apis.stream().filter(api -> {
                boolean flag = true;
                if (!new HashSet<>(request.getHttpMethods()).containsAll(api.getHttpMethods())) {
                    return false;
                }
                if (api.getUrlEndpoints().stream()
                        .noneMatch(u -> (u.getHost().equals(url.getHost()) &&
                                u.getPort().equals(String.valueOf(url.getPort()))))) {
                    return false;
                }
                if (!pathMatcher.match(url.getPath(), api.getUri())) {
                    return false;
                }
                return flag;
            });

            List<String> appIds = filterApiStream.map(ApiInfo::getAppId).distinct().collect(Collectors.toList());
            if (appIds.size() != 1) {
                throw new BusinessException(ResultCodeEnum.API_MERGE_ERROR, "api 不属于同一个应用");
            }

            List<String> mergeApiIds = apis.stream().filter(api -> {
                boolean flag = true;
                if (!new HashSet<>(request.getHttpMethods()).containsAll(api.getHttpMethods())) {
                    return false;
                }
                if (api.getUrlEndpoints().stream()
                        .noneMatch(u -> (u.getHost().equals(url.getHost()) &&
                                u.getPort().equals(String.valueOf(url.getPort()))))) {
                    return false;
                }
                if (!pathMatcher.match(url.getPath(), api.getUri())) {
                    return false;
                }
                return flag;
            }).map(ApiInfo::getId).collect(Collectors.toList());
            entity = request.toMergeEntity(mergeApiIds);
            if (mergeApiIds.size() <= 1) {
                throw new BusinessException(ResultCodeEnum.API_MERGE_ERROR, "合并api数量为1");
            }
        } else {
            entity = request.toIgnoreEntity();
        }

        ApiMerge apiMerge = apiMergeService.saveOne(entity);

        return apiMerge;
    }

    private void checkDuplicateMatch(String urlStr) {
        URL url;
        try {
            url = new URL(urlStr);
        } catch (MalformedURLException e) {
            throw new BusinessException("不是有效的 url");
        }
        checkDuplicateUriMatch(url.getPath());
    }

    private void checkDuplicateUriMatch(String uriStr) {
        List<ApiMerge> apiMerges = apiMergeService.list();

        List<String> apiMergeUris = apiMerges.stream().map(ApiMerge::getUriReg).collect(Collectors.toList());
        String bestMatch = ApiUrlUtils.findBestMatch(apiMergeUris, uriStr);
        if (StrUtil.isNotBlank(bestMatch)) {
            throw new BusinessException("重复的策略:" + bestMatch);
        }
        for (String mergeUris : apiMergeUris) {
            if (ApiUrlUtils.isMatch(mergeUris, uriStr)) {
                throw new BusinessException("重复的策略:" + mergeUris);
            }
        }
        //
        //
        // for (ApiMerge apiMerge : apiMerges) {
        //     String existUrlMatch = apiMerge.getUriReg();
        //     String uriReg;
        //     try {
        //         URL existUrl = new URL(existUrlMatch);
        //         uriReg = existUrl.getPath();
        //     } catch (MalformedURLException e) {
        //         throw new BusinessException("不是有效的 url");
        //     }
        //
        //     if (uriStr.matches(uriReg)) {
        //         throw new BusinessException("重复的策略:" + existUrlMatch);
        //     }
        // }
    }

    public List<String> saveMergedApi(ApiMerge apiMerge) {
        List<ApiFullInfo> apiInfos = apiInfoService.listWithApp(apiMerge.getAppId());

        List<String> patterns = Collections.singletonList(apiMerge.getUriReg());
        apiInfos = apiInfos.stream()
                .filter(api -> ApiUrlUtils.findBestMatch(patterns, api.getUri()) != null)
                .collect(Collectors.toList());

        List<String> apiIds = apiInfos.stream().map(ApiInfo::getId).distinct().collect(Collectors.toList());
        apiInfoService.deleteByIds(apiIds);

        String appId = apiMerge.getAppId();

        ApiInfo apiInfo = ApiInfo.ofMerge(apiMerge.getId(),
                apiMerge.getApiName(),
                appId,
                apiMerge.getHttpMethods(),
                apiMerge.getUriReg());
        apiInfoService.save(apiInfo);
        apiInfoService.updateMergeId(apiIds, apiMerge.getId());
        log.info("[API][MERGE] saveMergedApi: {}", apiMerge);
        return apiIds;
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.FULL_UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"策略 #{#result.name} 状态变更为 禁用"}
    )
    public ApiMerge updateOne(UpdateApiMergeRequest request) {
        checkCanUpdate(request.getId());
        checkDuplicateName(request.getName());
        checkDuplicateMatch(request.getUrlReg());
        ApiMerge entity = request.toEntity();
        return apiMergeService.updateOne(entity);
    }

    // @OperationLogAnnotation(
    //         operationType = OperationTypeEnum.UPDATE,
    //         resourceType = ResourceTypeEnum.API_MERGE,
    //         spelArgs = {"策略 #{#result.name} 状态变更为 启用"}
    // )
    @Transactional(rollbackFor = Exception.class)
    public ApiMerge enable(String id) {
        checkCanUpdate(id);
        ApiMerge byId = apiMergeService.getById(id);
        // 启用过了, 不再操作
        if (byId.getEnableTime() != null) {
            return byId;
        }

        apiMergeService.updateStatusById(id, true);
        // 新增一个api, 修改n个 api 合并状态为合并

        return mergeAndUpdateData(byId);
    }

    public ApiMerge mergeAndUpdateData(ApiMerge byId) {
        // 新增一个api, 没有保证原子性
        List<String> mergedApis = saveMergedApi(byId);
        // 涉敏次数重置
        sensitiveApiService.resetCount(mergedApis);
        // 异常行为次数重置
        abrtService.resetCount(mergedApis);

        String toApiId = byId.getId();
        // 更新涉敏绑定
        sensitiveApiService.updateApiId(mergedApis, toApiId);
        sensitiveLogService.updateApiId(mergedApis, toApiId);
        sensitiveWhiteListService.updateApiId(mergedApis, toApiId);
        // 更新威胁绑定
        riskLogNewService.updateApiId(mergedApis, toApiId);
        // 加密绑定
        apiDecryptService.updateApiId(mergedApis, toApiId);
        // 更新异常行为绑定
        abrService.updateApiId(mergedApis, toApiId);
        abrtService.updateApiId(mergedApis, toApiId);
        // 黑白名单
        blocklistService.updateApiId(mergedApis, toApiId);
        // 更新日志
        nginxAccessLogService.updateApiId(mergedApis, toApiId);
        // 更新
        log.info("[API][MERGE] apis {} => api {}", mergedApis, toApiId);
        return byId;
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"策略 #{#result.name} 状态变更为 禁用"}
    )
    public ApiMerge disable(String id) {
        checkCanUpdate(id);
        ApiMerge byId = apiMergeService.getById(id);
        apiMergeService.updateStatusById(id, false);
        return byId;
    }

    private void checkCanUpdate(String id) {
        if (!apiMergeService.canUpdate(id)) {
            throw new BusinessException(ResultCodeEnum.CAN_NOT_UPDATE);
        }
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#result}"}
    )
    @Transactional(rollbackFor = Exception.class)
    public ApiMerge deleteOne(String id) {
        ApiMerge apiMerge = apiMergeService.detail(id);
        apiMergeService.deleteOne(id);
        // todo 还原恶心人的操作
        return apiMerge;
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.DELETE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#result}"}
    )
    @Transactional(rollbackFor = Exception.class)
    public List<ApiMerge> deleteBatch(List<String> ids) {
        ids.forEach(this::deleteOne);
        return apiMergeService.getByIds(ids);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#result}"}
    )
    @Transactional(rollbackFor = Exception.class)
    public List<ApiMerge> enableBatch(List<String> ids) {
        ids.forEach(this::enable);
        return apiMergeService.getByIds(ids);
    }

    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.API_MERGE,
            spelArgs = {"#{#result}"}
    )
    @Transactional(rollbackFor = Exception.class)
    public List<ApiMerge> disableBatch(List<String> ids) {
        ids.forEach(this::disable);
        return apiMergeService.getByIds(ids);
    }

    public static void main(String[] args) throws MalformedURLException {
        URL url = new URL("https:/************:1111/api/v1/{xxx}");
        System.out.println(pathMatcher.match(url.getPath(), "/api/v1/listArticle"));
    }

    public ApiMergeAnalyzeResponse tryMerge(List<String> ids) {
        List<ApiInfo> apis = apiInfoService.getByIds(ids);
        if (apis.stream().anyMatch(api -> Constant.Api.SOURCE_APP_MERGE.equals(api.getSource()))) {
            return ApiMergeAnalyzeResponse.ofNotMergeable();
        }

        List<Integer> apiUriPartLengths =
                apis.stream().map(api -> api.getUri().split("/").length)
                        .distinct()
                        .collect(Collectors.toList());
        if (apiUriPartLengths.size() != 1) {
            return ApiMergeAnalyzeResponse.ofNotMergeable();
        }

        // 依次判断 uri 的各部分
        List<String> uriParts = new ArrayList<>();
        for (int i = 1; i < apiUriPartLengths.get(0) + 1; i++) {
            uriParts.add("");
        }
        for (int i = 1, uriPartsSize = uriParts.size(); i < uriPartsSize; i++) {
            uriParts.set(i, "");
            for (ApiInfo api : apis) {
                String[] splitUri = api.getUri().split("/");
                if (uriParts.get(i).isEmpty()) {
                    uriParts.set(i, splitUri[i]);
                    continue;
                }
                if (!splitUri[i].equals(uriParts.get(i))) {
                    uriParts.set(i, String.format("{param%s}", i + 1));
                }
            }
        }
        String resultUri = StrUtil.join("/", uriParts);

        checkDuplicateUriMatch(resultUri);

        List<ApplicationResponse> apps = applicationBusinessService.listWithBizName();
        Map<String, String> appMap = apps.stream().collect(Collectors.toMap(ApplicationResponse::getApplicationId,
                ApplicationResponse::getName, (a, b) -> a));

        List<MatchMergeApiResponse> matchMergeApiResponseList =
                apis.stream()
                        .map(api -> {
                            MatchMergeApiResponse response = new MatchMergeApiResponse();
                            response.setApiId(api.getId());
                            response.setApiName(api.getName());
                            response.setAppId(api.getAppId());
                            response.setAppName(appMap.get(api.getAppId()));
                            response.setHttpMethod(api.getHttpMethods());
                            response.setUri(api.getUri());
                            return response;
                        }).collect(Collectors.toList());

        ApiMergeAnalyzeResponse re = new ApiMergeAnalyzeResponse();
        re.setMergeable(true);
        re.setUri(resultUri);
        re.setMatchedApis(matchMergeApiResponseList);
        return re;
    }
}
