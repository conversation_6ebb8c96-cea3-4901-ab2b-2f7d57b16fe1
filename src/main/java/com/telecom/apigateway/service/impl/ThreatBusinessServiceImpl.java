package com.telecom.apigateway.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.telecom.apigateway.common.Constant;
import com.telecom.apigateway.common.OperationLogAnnotation;
import com.telecom.apigateway.common.excption.BusinessException;
import com.telecom.apigateway.mapper.RiskLogNewMapper;
import com.telecom.apigateway.model.entity.*;
import com.telecom.apigateway.model.enums.ApplicationTypeEnum;
import com.telecom.apigateway.model.enums.OperationTypeEnum;
import com.telecom.apigateway.model.enums.RecordTypeEnum;
import com.telecom.apigateway.model.enums.ResourceTypeEnum;
import com.telecom.apigateway.model.enums.RiskLevelEnum;
import com.telecom.apigateway.model.vo.request.*;
import com.telecom.apigateway.model.vo.response.*;
import com.telecom.apigateway.service.*;
import com.telecom.apigateway.utils.MapUtil;
import com.telecom.apigateway.utils.PageUtils;
import com.telecom.apigateway.utils.ValidationUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.telecom.apigateway.common.ResultCodeEnum.*;

@Service
@AllArgsConstructor
public class ThreatBusinessServiceImpl implements ThreatBusinessService {
    private final ApiInfoService apiInfoService;
    private final ApplicationService applicationService;
    private final RuleService ruleService;
    private final ApplicationPermissionService applicationPermissionService;
    private final OperationLogService operationLogService;
    private final BlocklistBusinessService blocklistBusinessService;
    private final RiskLogNewService riskLogNewService;
    private final RiskLogNewMapper riskLogNewMapper;

    @Override
    public Page<QueryThreatResponse> query(QueryThreatRequest request) {
        List<String> permissionList = StpUtil.getPermissionList();
        if (CollectionUtils.isEmpty(permissionList)) {
            return new Page<>();
        }
        
        RecordTypeEnum recordType = Optional.ofNullable(request.getRecordType()).orElse(RecordTypeEnum.THREAT);
        
        // 分页参数
        int pageNum = Optional.ofNullable(request.getPageNum()).orElse(1);
        int pageSize = Optional.ofNullable(request.getPageSize()).orElse(20);
        
        // 权限检查 - 如果不包含威胁清单权限，仅能查看第一页50条数据（用于大屏）
        List<String> pagePermission = applicationPermissionService.getPagePermission();
        if (!pagePermission.contains("THREAT_LIST")) {
            pageSize = Math.min(pageSize, 50);
            pageNum = 1;
        }
        
        // 构建查询参数
        List<String> belongApplication = applicationService.getApplicationPermissions(request.getBelongApplication(), permissionList);
        List<String> appIds = CollectionUtils.isNotEmpty(belongApplication) ? belongApplication : null;
        
        // 风险等级转换
        List<Integer> riskLevels = null;
        if (CollectionUtils.isNotEmpty(request.getRiskLevel())) {
            riskLevels = request.getRiskLevel();
        }

        Page<RiskLogNew> records = riskLogNewMapper.selectUniquePageByCondition(
                new Page<>(pageNum, pageSize),
                request.getStartTime(),
                request.getEndTime(),
                appIds,
                request.getAttackType(),
                request.getClientIp(),
                request.getUri(),
                request.getRiskLogId(),
                request.getDealt(),
                RecordTypeEnum.FALSE_POSITIVE.equals(recordType), // isFalsePositive
                riskLevels
        );
        
        // 转换结果
        List<QueryThreatResponse> responses = convertRiskLogNewToThreatResponse(records.getRecords());
        
        return PageUtils.convertPage(records, responses);
    }
    
    /**
     * 将RiskLogNew转换为QueryThreatResponse
     */
    private List<QueryThreatResponse> convertRiskLogNewToThreatResponse(List<RiskLogNew> riskLogs) {
        if (CollectionUtils.isEmpty(riskLogs)) {
            return new ArrayList<>();
        }
        
        // 获取规则映射
        Map<String, Rule> ruleMap = MapUtil.toMapByUnionParams(Rule::getType, ruleService.list());
        
        // 获取应用映射
        List<Application> applications = applicationService.list();
        Map<String, Application> applicationMap = MapUtil.toMapByUnionParams(Application::getApplicationId, applications);
        
        // 获取API映射
        List<String> apiIds = riskLogs.stream().map(RiskLogNew::getApiId).distinct().collect(Collectors.toList());
        Map<String, ApiInfo> apiMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(apiIds)) {
            List<ApiInfo> apis = apiInfoService.listByIds(apiIds);
            apiMap = apis.stream().collect(Collectors.toMap(ApiInfo::getId, api -> api));
        }
        
        List<QueryThreatResponse> responses = new ArrayList<>();
        for (RiskLogNew riskLog : riskLogs) {
            // 获取规则信息
            Rule rule = ruleMap.get(riskLog.getRuleType());
            String attackType = rule != null ? rule.getAttackType() : "其他";
            
            // 获取API信息
            ApiInfo apiInfo = apiMap.get(riskLog.getApiId());
            String uri = apiInfo != null ? apiInfo.getUri() : riskLog.getUri();
            
            // 构建地区信息
            String clientRegion = buildRegion(riskLog.getClientCountry(), riskLog.getClientCity());
            
            QueryThreatResponse response = QueryThreatResponse.builder()
                    .uri(uri)
                    .belongApplication(convertApplicationName(riskLog.getAppId(), applicationMap))
                    .attackType(attackType)
                    .dealt("REJECT".equals(riskLog.getCrsDetectStatus()))
                    .clientIp(riskLog.getClientIp())
                    .clientPort(null) // RiskLogNew表中没有端口字段，如需要可添加
                    .clientRegion(clientRegion)
                    .attackedTime(riskLog.getLogTime())
                    .riskLogId(riskLog.getId())
                    .apiId(riskLog.getApiId())
                    .riskLevel(getRiskLevel(riskLog.getScore()))
                    .logId(riskLog.getLogId())
                    .targetAddress(riskLog.getUrl())
                    .targetRegion(null) // 如需要可以从其他字段获取
                    .falsePositiveReason(riskLog.getReason())
                    .processSuggestion(riskLog.getProcessSuggestion())
                    .requestLog(buildRequestLog(riskLog))
                    .responseLog(buildResponseLog(riskLog))
                    .build();
            responses.add(response);
        }
        
        return responses;
    }
    
    /**
     * 构建地区信息
     */
    private String buildRegion(String country, String city) {
        if (StringUtils.isBlank(country) && StringUtils.isBlank(city)) {
            return Constant.UNKNOWN_REGION;
        }
        
        StringBuilder region = new StringBuilder();
        if (StringUtils.isNotBlank(country) && !"UNKNOWN".equals(country)) {
            if ("INNER".equals(country)) {
                return Constant.INNER_REGION;
            }
            region.append(country);
        }
        
        if (StringUtils.isNotBlank(city) && !"UNKNOWN".equals(city) && !"INNER".equals(city)) {
            if (region.length() > 0) {
                region.append("-");
            }
            region.append(city);
        }
        
        return region.length() > 0 ? region.toString() : Constant.UNKNOWN_REGION;
    }
    
    /**
     * 构建请求日志
     */
    private String buildRequestLog(RiskLogNew riskLog) {
        StringBuilder requestLog = new StringBuilder();
        requestLog.append(riskLog.getScheme()).append(" ").append(riskLog.getUri()).append("\n");
        
        if (StringUtils.isNotBlank(riskLog.getRequestHeader())) {
            requestLog.append("Headers:\n").append(riskLog.getRequestHeader()).append("\n");
        }
        
        if (StringUtils.isNotBlank(riskLog.getRequestParam())) {
            requestLog.append("Params:\n").append(riskLog.getRequestParam()).append("\n");
        }
        
        if (StringUtils.isNotBlank(riskLog.getRequestBody())) {
            requestLog.append("Body:\n").append(riskLog.getRequestBody());
        }
        
        return requestLog.toString();
    }
    
    /**
     * 构建响应日志
     */
    private String buildResponseLog(RiskLogNew riskLog) {
        StringBuilder responseLog = new StringBuilder();
        responseLog.append("Status: ").append(riskLog.getStatusCode()).append("\n");
        
        if (StringUtils.isNotBlank(riskLog.getResponseHeader())) {
            responseLog.append("Headers:\n").append(riskLog.getResponseHeader()).append("\n");
        }
        
        if (StringUtils.isNotBlank(riskLog.getResponseData())) {
            responseLog.append("Body:\n").append(riskLog.getResponseData());
        }
        
        return responseLog.toString();
    }

    private Integer getRiskLevel(Integer score) {
        if (score >= 5) {
            return RiskLevelEnum.HIGH.getCode();
        } else if (score >= 3) {
            return RiskLevelEnum.MEDIUM.getCode();
        } else if (score > 0) {
            return RiskLevelEnum.LOW.getCode();
        } else {
            return RiskLevelEnum.SAFE.getCode();
        }
    }

    @Override
    public List<ThreatExcelResponse> export(QueryThreatRequest request) {
        request.setPageNum(1);
        List<QueryThreatResponse> records = query(request).getRecords();
        List<ThreatExcelResponse> threatExcelResponses = BeanUtil.copyToList(records, ThreatExcelResponse.class);
        threatExcelResponses.forEach((result) -> {
            result.setDealtName(result.getDealt() ? "已拦截" : "仅观察");
            result.setRiskLevelName(RiskLevelEnum.getNameByCode(result.getRiskLevel()));
        });
        return threatExcelResponses;
    }

    @Override
    public ThreatDetailResponse getByRiskId(String riskId) {
        // 从RiskLogNew表查询威胁详情
        RiskLogNew riskLog = riskLogNewService.getById(riskId);
        if (riskLog == null) {
            throw new BusinessException(THREAT_NOT_EXISTED);
        }

        String apiId = riskLog.getApiId();
        Optional<ApiInfo> api = apiInfoService.getOptById(apiId);
        if (!api.isPresent()) {
            throw new BusinessException(API_NOT_EXISTED);
        }
        ApiInfo apiInfo = api.get();

        Application application = getApplication(riskLog.getAppId());

        // 获取威胁等级
        Integer riskLevel = getRiskLevel(riskLog.getScore());

        // 获取规则信息
        Map<String, Rule> ruleMap = MapUtil.toMapByUnionParams(Rule::getType, ruleService.list());
        Rule rule = ruleMap.get(riskLog.getRuleType());
        
        Set<String> uniqueAttackTypes = new HashSet<>();
        Set<String> uniqueModules = new HashSet<>();

        if (rule != null) {
            uniqueAttackTypes.add(rule.getAttackType());
            uniqueModules.add(rule.getModule());
        }

        List<Application> applications = applicationService.list();
        Map<String, Application> applicationMap = MapUtil.toMapByUnionParams(Application::getApplicationId, applications);
        
        ThreatDetailResponse threatDetailResponse = new ThreatDetailResponse();
        threatDetailResponse.setRiskId(riskLog.getLogId());
        threatDetailResponse.setAttackType(new ArrayList<>(uniqueAttackTypes));
        threatDetailResponse.setDealt("REJECT".equals(riskLog.getCrsDetectStatus()));
        threatDetailResponse.setClientIP(riskLog.getClientIp());
        threatDetailResponse.setAttackedTime(riskLog.getLogTime());
        threatDetailResponse.setTargetAddress(riskLog.getUrl());
        threatDetailResponse.setUri(riskLog.getUri());
        threatDetailResponse.setBelongApplication(convertApplicationName(application.getApplicationId(), applicationMap));
        threatDetailResponse.setLogRequest(buildRequestLog(riskLog));
        threatDetailResponse.setLogResponse(buildResponseLog(riskLog));
        threatDetailResponse.setRiskLevel(riskLevel);
        threatDetailResponse.setGuardModule(new ArrayList<>(uniqueModules));
        threatDetailResponse.setDecryptRequestBody(riskLog.getDecryptedRequestBody());
        threatDetailResponse.setIsEncryptApi(apiInfo.getEncrypted());
        return threatDetailResponse;
    }

    @Override
    public List<AttackTypeQueryResponse> options() {
        // 查询所有规则并根据type去重
        return ruleService.list()
                .stream()
                .map((rule) -> {
                    AttackTypeQueryResponse response = new AttackTypeQueryResponse();
                    response.setAttackType(rule.getAttackType());
                    response.setRuleId(rule.getType());
                    return response;
                }).collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(AttackTypeQueryResponse::getRuleId))), ArrayList::new));
    }

    @Override
    public Map<String, List<? extends BaseLabelResponse>> threatCount(ThreatCountRequest request) {
        // 构建查询条件
        com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<RiskLogNew> queryWrapper = 
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
        
        // 时间范围
        LocalDateTime startTime = Optional.ofNullable(request.getStartTime()).orElse(LocalDateTime.now().minusDays(7));
        LocalDateTime endTime = Optional.ofNullable(request.getEndTime()).orElse(LocalDateTime.now());
        queryWrapper.ge("log_time", startTime).le("log_time", endTime);
        
        // 应用筛选
        if (StringUtils.isNotBlank(request.getAppId())) {
            queryWrapper.eq("app_id", request.getAppId());
        }
        
        // 查询威胁记录（非误报）
        queryWrapper.isNull("reason");
        
        List<RiskLogNew> riskLogs = riskLogNewService.list(queryWrapper);
        
        Map<String, List<? extends BaseLabelResponse>> map = new HashMap<>();
        
        // 威胁等级统计
        List<BaseLabelResponse> threatCount = new ArrayList<>();
        for (RiskLevelEnum level : RiskLevelEnum.values()) {
            if (level.getCode() == 0) {
                continue;
            }
            long count = riskLogs.stream()
                    .filter(riskLog -> level.getCode().equals(getRiskLevel(riskLog.getScore())))
                    .count();
            threatCount.add(new BaseLabelResponse(level.getLabel(), (int) count));
        }
        threatCount.add(new BaseLabelResponse("威胁总数", riskLogs.size()));
        map.put("count", threatCount);

        // 攻击者Top5统计
        List<AttackerLabelResponse> attackerTop5 = riskLogs.stream()
                .collect(Collectors.groupingBy(RiskLogNew::getClientIp, Collectors.counting()))
                .entrySet()
                .stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .map(entry -> {
                    // 获取该IP的第一条记录来获取地区信息
                    RiskLogNew firstLog = riskLogs.stream()
                            .filter(log -> entry.getKey().equals(log.getClientIp()))
                            .findFirst()
                            .orElse(null);
                    String region = firstLog != null ? 
                            buildRegion(firstLog.getClientCountry(), firstLog.getClientCity()) : 
                            Constant.UNKNOWN_REGION;
                    return new AttackerLabelResponse(entry.getKey(), entry.getValue().intValue(), region);
                })
                .collect(Collectors.toList());
        map.put("attacker", attackerTop5);

        // 拦截状态统计
        Map<Boolean, List<RiskLogNew>> dealtGroups = riskLogs.stream()
                .collect(Collectors.groupingBy(log -> Optional.ofNullable(log.getIsDealt()).orElse(false)));
        List<BaseLabelResponse> dealtStatus = new ArrayList<>();
        dealtStatus.add(new BaseLabelResponse("已拦截", dealtGroups.getOrDefault(true, Collections.emptyList()).size()));
        dealtStatus.add(new BaseLabelResponse("仅观察", dealtGroups.getOrDefault(false, Collections.emptyList()).size()));
        map.put("dealtStatus", dealtStatus);

        // 攻击类型统计
        Map<String, Rule> ruleMap = MapUtil.toMapByUnionParams(Rule::getType, ruleService.list());
        List<BaseLabelResponse> attackType = riskLogs.stream()
                .collect(Collectors.groupingBy(RiskLogNew::getRuleType, Collectors.counting()))
                .entrySet()
                .stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .map(entry -> {
                    String typeName = Optional.ofNullable(ruleMap.get(entry.getKey()))
                            .map(Rule::getAttackType)
                            .orElse("已删除的检测规则");
                    return new BaseLabelResponse(typeName, entry.getValue().intValue());
                })
                .collect(Collectors.toList());
        map.put("attackType", attackType);

        // 威胁数量趋势统计
        List<BaseLabelResponse> trend = new ArrayList<>();
        long daysBetween = ChronoUnit.DAYS.between(startTime.toLocalDate(), endTime.toLocalDate());
        
        if (daysBetween <= 31) {
            // 按天统计
            LocalDate currentDate = startTime.toLocalDate();
            LocalDate endDate = endTime.toLocalDate();
            
            while (!currentDate.isAfter(endDate)) {
                final LocalDate dateToCheck = currentDate;
                long dayCount = riskLogs.stream()
                        .filter(log -> log.getLogTime().toLocalDate().equals(dateToCheck))
                        .count();
                trend.add(new BaseLabelResponse(
                        currentDate.format(java.time.format.DateTimeFormatter.ofPattern("MM-dd")), 
                        (int) dayCount
                ));
                currentDate = currentDate.plusDays(1);
            }
        } else {
            // 按月统计
            YearMonth currentMonth = YearMonth.from(startTime);
            YearMonth endMonth = YearMonth.from(endTime);
            
            while (!currentMonth.isAfter(endMonth)) {
                final YearMonth monthToCheck = currentMonth;
                long monthCount = riskLogs.stream()
                        .filter(log -> YearMonth.from(log.getLogTime()).equals(monthToCheck))
                        .count();
                trend.add(new BaseLabelResponse(
                        currentMonth.format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM")), 
                        (int) monthCount
                ));
                currentMonth = currentMonth.plusMonths(1);
            }
        }
        map.put("trend", trend);

        return map;
    }

    @Override
    @Transactional
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.THREAT_LIST,
            description = "标记威胁日志 #{#request.logId} 为误报，原因：#{#request.reason}"
    )
    public void falsePositiveReport(FalsePositiveRequest request) {
        // 从RiskLogNew表更新误报信息
        RiskLogNew riskLog = riskLogNewService.getById(request.getLogId());
        if (riskLog == null) {
            throw new BusinessException(THREAT_NOT_EXISTED);
        }

        riskLog.setReason(request.getReason());
        riskLog.setProcessSuggestion(request.getProcessSuggestion());
        riskLog.setUpdateTime(LocalDateTime.now());
        riskLog.setUpdateUser(StpUtil.getLoginIdAsString());

        riskLogNewService.updateById(riskLog);
    }

    @Override
    @Transactional
    @OperationLogAnnotation(
            operationType = OperationTypeEnum.UPDATE,
            resourceType = ResourceTypeEnum.THREAT_LIST,
            description = "标记威胁日志 #{#request.falsePositive?.logId} 为误报，原因：#{#request.falsePositive?.reason}"
    )
    public void falsePositiveReport(FalsePositiveAndAccessListRequest request) {
        // 先做白名单处理，避免事务出错
        BlocklistRequest blocklistRequest = request.getBlocklistRequest();
        if (Objects.nonNull(blocklistRequest)) {
            ValidationUtil.validate(request);
            String type = blocklistRequest.getType();
            if (!"White".equals(type)) {
                throw new BusinessException("仅支持加入白名单");
            }
            blocklistBusinessService.add(blocklistRequest);
        }
        FalsePositiveRequest falsePositive = request.getFalsePositive();
        falsePositiveReport(falsePositive);
    }

    @Override
    @OperationLogAnnotation(
        operationType = OperationTypeEnum.UPDATE,
        resourceType = ResourceTypeEnum.THREAT_LIST,
        description = "取消威胁日志 #{#logId} 的误报标记"
    )
    public void falsePositiveCancel(String logId) {
        RiskLogNew riskLog = riskLogNewService.getById(logId);
        if (riskLog == null) {
            throw new BusinessException(THREAT_NOT_EXISTED);
        }

        riskLog.setReason("");
        riskLog.setProcessSuggestion("");
        riskLog.setUpdateTime(LocalDateTime.now());
        riskLog.setUpdateUser(StpUtil.getLoginIdAsString());

        riskLogNewService.updateById(riskLog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void falsePositiveBatchReport(BatchFalsePositiveAndAccessListRequest request) {
        // 先做白名单处理，避免事务出错
        BlocklistRequest blocklistRequest = request.getBlocklistRequest();
        if (Objects.nonNull(blocklistRequest)) {
            ValidationUtil.validate(request);
            String type = blocklistRequest.getType();
            if (!"White".equals(type)) {
                throw new BusinessException("仅支持加入白名单");
            }
            blocklistBusinessService.add(blocklistRequest);
        }

        request.getLogIds().stream().distinct().forEach((logId) -> {
            FalsePositiveRequest falsePositive = new FalsePositiveRequest();
            falsePositive.setLogId(logId);
            falsePositive.setReason(request.getReason());
            falsePositive.setProcessSuggestion(request.getProcessSuggestion());
            falsePositiveReport(falsePositive);
        });
    }

    private ApiInfo getApiInfo(String apiId) {
        Optional<ApiInfo> optById = apiInfoService.getOptById(apiId);
        if (!optById.isPresent()) {
            throw new BusinessException(API_NOT_EXISTED);
        }
        return optById.get();
    }

    private Application getApplication(String applicationId) {
        Optional<Application> optById = applicationService.getByApplicationId(applicationId);
        if (!optById.isPresent()) {
            throw new BusinessException(APPLICATION_NOT_EXISTED);
        }
        return optById.get();
    }

    private String convertApplicationName(String applicationId, Map<String, Application> applicationMap) {
        List<String> names = new ArrayList<>();
        String nodeId = applicationId;
        List<Application> nonRootApplications = applicationMap.values().stream().filter((application -> Objects.nonNull(application.getParentId()))).collect(Collectors.toList());
        Map<String, List<Application>> parentMap = MapUtil.grouping(Application::getParentId, nonRootApplications);
        while (StringUtils.isNotBlank(nodeId) && applicationMap.containsKey(nodeId)) {
            Application application = applicationMap.get(nodeId);
            nodeId = application.getParentId();
            // 父应用下只有一个子应用，不显示基础分组
            // 没基础分组了bruh
//            if (parentMap.containsKey(nodeId) && parentMap.get(nodeId).size() == 1 && ApplicationTypeEnum.BASE_API.equals(application.getType())) {
//                continue;
//            }
            names.add(application.getName());
        }
        Collections.reverse(names);
        return names.isEmpty() ? "未知" : String.join("-", names);
    }
}
